import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    environment: 'node', // Use node environment for Playwright tests
    globals: true,
    setupFiles: ['./e2e/setup.ts'], // Only include e2e setup, not the mocked tests/setup.ts
    include: [
      'e2e/**/*.e2e.ts', // All e2e tests now use Playwright/Electron setup
    ],
    exclude: [
      'tests/**/*',                 // Exclude unit tests
    ],
    testTimeout: 120000, // 2 minutes for e2e tests
    hookTimeout: 60000,  // 1 minute for setup/teardown hooks (<PERSON><PERSON> needs more time)
    // Playwright tests should run sequentially to avoid conflicts
    fileParallelism: false,
    maxConcurrency: 1
  },
  define: {
    global: 'globalThis'
  }
});
